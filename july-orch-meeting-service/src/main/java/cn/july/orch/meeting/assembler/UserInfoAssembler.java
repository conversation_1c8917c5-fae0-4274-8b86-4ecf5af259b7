package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.config.UserInfoDTO;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import com.lark.oapi.service.contact.v3.model.AvatarInfo;
import com.lark.oapi.service.contact.v3.model.User;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;
import java.util.Optional;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserInfoAssembler {

    UserInfoDTO resp2DTO(GetUserInfoRespBody respBody);

    default FSUserInfoDTO user2DTO(User user){
        return FSUserInfoDTO.builder()
                .openId(user.getOpenId())
                .name(user.getName())
                .avatarUrl(Optional.of(user.getAvatar()).map(AvatarInfo::getAvatarOrigin).orElse(null))
                .build();
    }

    default List<FSUserInfoDTO> user2DTO(List<User> list){
        return list.stream().map(this::user2DTO).collect(java.util.stream.Collectors.toList());
    }

}
