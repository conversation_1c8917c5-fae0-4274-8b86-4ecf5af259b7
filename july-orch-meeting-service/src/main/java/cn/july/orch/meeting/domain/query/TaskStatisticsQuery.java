package cn.july.orch.meeting.domain.query;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 任务统计查询
 * @date 2025-01-28
 */
@Data
public class TaskStatisticsQuery {

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 统计类型
     */
    private String statisticsType;
}
