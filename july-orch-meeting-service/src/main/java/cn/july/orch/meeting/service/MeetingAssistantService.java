package cn.july.orch.meeting.service;

import cn.july.orch.meeting.common.AgentConstants;
import cn.july.orch.meeting.domain.command.QuestionAnswerCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 会议助手服务
 */
@Slf4j
@Service
public class MeetingAssistantService {

    @Resource
    private AgentInvokeService agentInvokeService;

    /**
     * 问答助手应用ID
     */
    private static final String QA_APP_ID = "688c63f47e9027870e39c7dd";

    /**
     * 问答助手（流式）
     */
    public Flux<String> answerStream(QuestionAnswerCommand command) {
        log.info("收到问答请求，问题：{}", command.getQuestion());

        try {
            // 调用智能体获取回答
            return agentInvokeService.invokeStreamGetAnswerStream(
                QA_APP_ID,
                command.getQuestion(),
                null,
                AgentConstants.QA_AUTHORIZATION
            );

        } catch (Exception e) {
            log.error("问答失败，问题：{}", command.getQuestion(), e);
            return Flux.error(new RuntimeException("问答失败：" + e.getMessage()));
        }
    }
}