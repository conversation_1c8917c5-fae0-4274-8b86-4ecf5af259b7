package cn.july.orch.meeting.service;

import cn.july.orch.meeting.assembler.NewMeetingConverter;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议领域服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NewMeetingDomainService {

    private final NewMeetingMapper newMeetingMapper;
    private final NewMeetingConverter newMeetingConverter;

    /**
     * 创建会议
     */
    public void createMeeting(NewMeeting meeting) {
        // 设置初始状态
        meeting.setStatus(NewMeetingStatusEnum.NOT_STARTED);
        
        // 保存会议
        newMeetingMapper.insert(newMeetingConverter.toPO(meeting));
    }

    /**
     * 更新会议
     */
    public void updateMeeting(NewMeeting meeting) {
        newMeetingMapper.updateById(newMeetingConverter.toPO(meeting));
    }

    /**
     * 删除会议
     */
    public void deleteMeeting(Long id) {
        newMeetingMapper.deleteById(id);
    }

    /**
     * 根据ID查找会议
     */
    public NewMeeting findById(Long id) {
        NewMeetingPO po = newMeetingMapper.selectById(id);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    /**
     * 根据飞书日程事件ID查找会议
     */
    public NewMeeting findByFsCalendarEventId(String fsCalendarEventId) {
        NewMeetingPO po = newMeetingMapper.findByFsCalendarEventId(fsCalendarEventId);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    /**
     * 根据飞书会议ID查找会议
     */
    public NewMeeting findByFsMeetingId(String fsMeetingId) {
        NewMeetingPO po = newMeetingMapper.findByFsMeetingId(fsMeetingId);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    /**
     * 更新会议状态
     */
    public void updateMeetingStatus(Long id, NewMeetingStatusEnum status) {
        newMeetingMapper.updateStatus(id, status);
    }

    /**
     * 更新飞书相关信息
     */
    public void updateFeishuInfo(Long id, String fsCalendarEventId, String fsMeetingId, String meetingUrl) {
        newMeetingMapper.updateFeishuInfo(id, fsCalendarEventId, fsMeetingId, meetingUrl);
    }

    /**
     * 更新会议编号和飞书会议ID
     */
    public void updateMeetingNoAndFsMeetingId(Long id, String meetingNo, String fsMeetingId) {
        newMeetingMapper.updateMeetingNoAndFsMeetingId(id, meetingNo, fsMeetingId);
    }

    /**
     * 更新会议编号、飞书会议ID和会议链接
     */
    public void updateMeetingInfo(Long id, String meetingNo, String fsMeetingId, String meetingUrl) {
        newMeetingMapper.updateMeetingInfo(id, meetingNo, fsMeetingId, meetingUrl);
    }

    /**
     * 更新妙计链接
     */
    public void updateMinuteUrl(Long id, String minuteUrl) {
        newMeetingMapper.updateMinuteUrl(id, minuteUrl);
    }

    /**
     * 分页查询会议
     */
    public List<NewMeeting> findPage(String meetingName, Integer status, Integer priorityLevel,
                                   Long meetingPlanId, Long meetingStandardId, String startTimeFrom, 
                                   String startTimeTo, String createUserId, Integer pageNum, Integer pageSize) {
        IPage<NewMeetingPO> page = newMeetingMapper.findPage(meetingName, status, priorityLevel, meetingPlanId,
                meetingStandardId, startTimeFrom, startTimeTo, createUserId,
                pageNum, pageSize);
        return newMeetingConverter.toEntityList(page.getRecords());
    }

    /**
     * 统计会议数量
     */
    public Long count(String meetingName, Integer status, Integer priorityLevel,
                     Long meetingPlanId, Long meetingStandardId, String startTimeFrom, 
                     String startTimeTo, String createUserId) {
        return newMeetingMapper.count(meetingName, status, priorityLevel, meetingPlanId,
                                        meetingStandardId, startTimeFrom, startTimeTo, createUserId);
    }
} 