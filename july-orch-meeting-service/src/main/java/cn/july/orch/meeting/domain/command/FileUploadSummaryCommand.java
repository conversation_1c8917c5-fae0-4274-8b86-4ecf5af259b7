package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Assistant
 * @description 文件上传汇总命令
 */
@Data
@ApiModel("文件上传汇总命令")
public class FileUploadSummaryCommand {

    @ApiModelProperty(value = "上传的文件", required = true)
    @NotNull(message = "文件不能为空")
    private org.springframework.web.multipart.MultipartFile file;

    @ApiModelProperty(value = "智能体应用ID")
    private String appId;

    @ApiModelProperty(value = "自定义提示词")
    private String customPrompt;

    @ApiModelProperty(value = "文件平台标识")
    private String filePlatform;

    @ApiModelProperty(value = "关注点字符串")
    private String focusPoints;

    @ApiModelProperty(value = "字数限制", example = "500")
    private Integer words = 500;
}
