package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.command.QuestionAnswerCommand;
import cn.july.orch.meeting.service.MeetingAssistantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 会议助手控制器
 */
@Api(tags = "会议助手")
@Slf4j
@RestController
@RequestMapping("/assistant")
public class MeetingAssistantController {

    @Resource
    private MeetingAssistantService meetingAssistantService;

    @PostMapping(value = "/qa", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("问答助手")
    public Flux<String> answer(@Validated @RequestBody QuestionAnswerCommand command) {
        log.info("收到问答请求，问题：{}", command.getQuestion());
        return meetingAssistantService.answerStream(command);
    }
}