package cn.july.orch.meeting.domain.command;

import cn.july.orch.meeting.enums.PriorityLevelEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议更新命令
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewMeetingUpdateCommand {

    @ApiModelProperty(value = "会议ID", required = true)
    @NotNull(message = "会议ID不能为空")
    private Long id;

    @ApiModelProperty(value = "会议名称", required = true)
    @NotBlank(message = "会议名称不能为空")
    private String meetingName;

    @ApiModelProperty(value = "会议描述")
    private String meetingDescription;

    @ApiModelProperty(value = "会议开始时间", required = true)
    @NotNull(message = "会议开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "会议结束时间", required = true)
    @NotNull(message = "会议结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty(value = "会议地点")
    private String meetingLocation;

    @ApiModelProperty(value = "参会人员列表（用户ID列表）", required = true)
    @NotNull(message = "参会人员列表不能为空")
    private List<String> attendees;

    @ApiModelProperty(value = "主持人ID")
    private String hostUserId;

    @ApiModelProperty(value = "记录员ID")
    private String recorderUserId;
} 