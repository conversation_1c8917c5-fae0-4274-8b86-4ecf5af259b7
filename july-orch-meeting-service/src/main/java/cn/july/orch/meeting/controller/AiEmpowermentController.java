package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.command.FileUploadSummaryCommand;
import cn.july.orch.meeting.service.AiEmpowermentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description AI赋能控制器
 */
@Api(tags = "AI赋能")
@Slf4j
@RestController
@RequestMapping("/ai")
public class AiEmpowermentController {

    @Resource
    private AiEmpowermentService aiEmpowermentService;

    @PostMapping(value = "/file/upload-summary", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation(value = "文件上传汇总", notes = "上传文件并通过AI智能体进行汇总提炼，自动获取文件contentType")
    public Flux<String> fileUploadSummary(@RequestParam("file") MultipartFile file,
                                       @RequestParam(value = "appId", required = false) String appId,
                                       @RequestParam(value = "customPrompt", required = false) String customPrompt,
                                       @RequestParam(value = "filePlatform", required = false) String filePlatform,
                                       @RequestParam(value = "focusPoints", required = false) String focusPoints,
                                       @RequestParam(value = "words", required = false, defaultValue = "500") Integer words) {
        log.info("收到文件上传汇总请求，文件名：{}，大小：{} bytes",
                file.getOriginalFilename(), file.getSize());

        FileUploadSummaryCommand command = new FileUploadSummaryCommand();
        command.setFile(file);
        command.setAppId(appId);
        command.setCustomPrompt(customPrompt);
        command.setFilePlatform(filePlatform);
        command.setFocusPoints(focusPoints);
        command.setWords(words);

        return aiEmpowermentService.fileUploadSummaryStream(command);
    }
}