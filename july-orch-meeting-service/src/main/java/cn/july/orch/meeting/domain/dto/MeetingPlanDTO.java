package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划详情DTO
 * @date 2025-01-24
 */
@Data
public class MeetingPlanDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议标题
     */
    private String title;

    /**
     * 会议描述
     */
    private String description;

    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 参会人员
     */
    private List<String> attendees;

    /**
     * 主持人
     */
    private String host;

    /**
     * 记录人
     */
    private String recorder;

    /**
     * 状态
     */
    private MeetingPlanStatusEnum status;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
