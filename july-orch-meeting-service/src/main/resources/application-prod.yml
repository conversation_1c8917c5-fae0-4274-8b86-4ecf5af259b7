knife4j:
  enable: false
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://************:6379",
            "password": "July123456.",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
dromara:
  x-file-storage: #文件存储配置
    default-platform: minio-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: ryShVeAq1Fv5G8KX1nKn
        secret-key: FwwF7aQ4q4BqFwseidw8H3IcwOYq71QBWa6VCf5S
        end-point: http://coe-file.pengfeijituan.com:9002/
        bucket-name: meiye-safe
        domain: http://coe-file.pengfeijituan.com:9002/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: ${spring.application.name}/
feishu:
  mini:
    appId: cli_a808965153f95013
    appSecret: xrOQJck37DRjTYvjyN5moR7glft5yaXD
july:
  database:
    multi:
      db:
        pf_orch_meiye:
          primary: true
          master:
            jdbcUrl: ***********************************************************************************************************************************************************************************
            username: root
            password: July123456.
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10
