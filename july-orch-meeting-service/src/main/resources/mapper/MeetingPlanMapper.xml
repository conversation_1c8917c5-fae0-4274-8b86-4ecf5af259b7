<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingPlanMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="plan_name" property="planName" jdbcType="VARCHAR"/>
        <result column="plan_description" property="planDescription" jdbcType="VARCHAR"/>
        <result column="planned_start_time" property="plannedStartTime" jdbcType="TIMESTAMP"/>
        <result column="planned_end_time" property="plannedEndTime" jdbcType="TIMESTAMP"/>
        <result column="planned_duration" property="plannedDuration" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="meeting_standard_id" property="meetingStandardId" jdbcType="BIGINT"/>
        <result column="priority_level" property="priorityLevel" jdbcType="VARCHAR"/>
        <result column="department_id" property="departmentId" jdbcType="BIGINT"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="business_meeting_id" property="businessMeetingId" jdbcType="BIGINT"/>
        <result column="attendees" property="attendees" jdbcType="VARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="meeting_location" property="meetingLocation" jdbcType="VARCHAR"/>
        <result column="advance_notice_sent" property="advanceNoticeSent" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, plan_name, plan_description, planned_start_time, planned_end_time, planned_duration,
        status, meeting_standard_id, priority_level, department_id, department_name, business_meeting_id,
        attendees, meeting_location, advance_notice_sent, create_user_id, create_user_name, create_time,
        update_user_id, update_user_name, update_time
    </sql>

    <!-- 会议规划列表DTO结果映射 -->
    <resultMap id="MeetingPlanListDTOResultMap" type="cn.july.orch.meeting.domain.dto.MeetingPlanListDTO">
        <id column="id" property="id"/>
        <result column="planName" property="planName"/>
        <result column="planDescription" property="planDescription"/>
        <result column="plannedStartTime" property="plannedStartTime"/>
        <result column="plannedEndTime" property="plannedEndTime"/>
        <result column="plannedDuration" property="plannedDuration"/>
        <result column="status" property="status"/>
        <result column="meetingStandardId" property="meetingStandardId"/>
        <result column="meetingStandardName" property="meetingStandardName"/>
        <result column="priorityLevel" property="priorityLevel"/>
        <result column="departmentName" property="departmentName"/>
        <result column="businessMeetingName" property="businessMeetingName"/>
        <result column="meetingLocation" property="meetingLocation"/>
        <result column="attendees" property="attendees" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="attendeeCount" property="attendeeCount"/>
        <result column="createUserName" property="createUserName"/>
        <result column="createTime" property="createTime"/>
    </resultMap>

    <!-- 分页查询会议规划列表 -->
    <select id="queryPage" resultMap="MeetingPlanListDTOResultMap">
        SELECT
            mp.id,
            mp.plan_name AS planName,
            mp.plan_description AS planDescription,
            mp.planned_start_time AS plannedStartTime,
            mp.planned_end_time AS plannedEndTime,
            mp.planned_duration AS plannedDuration,
            mp.status,
            mp.meeting_standard_id AS meetingStandardId,
            ms.standard_name AS meetingStandardName,
            mp.priority_level AS priorityLevel,
            mp.department_name AS departmentName,
            NULL AS businessMeetingName,
            mp.meeting_location AS meetingLocation,
            mp.attendees,
            CASE
                WHEN mp.attendees IS NULL THEN 0
                ELSE JSON_LENGTH(mp.attendees)
            END AS attendeeCount,
            mp.create_user_name AS createUserName,
            mp.create_time AS createTime
        FROM meeting_plan mp
        LEFT JOIN meeting_standard ms ON mp.meeting_standard_id = ms.id
        WHERE 1=1
        <if test="query.planName != null and query.planName != ''">
            AND mp.plan_name LIKE CONCAT('%', #{query.planName}, '%')
        </if>
        <if test="query.status != null">
            AND mp.status = #{query.status}
        </if>
        <if test="query.meetingStandardId != null">
            AND mp.meeting_standard_id = #{query.meetingStandardId}
        </if>
        <if test="query.priorityLevel != null">
            AND mp.priority_level = #{query.priorityLevel}
        </if>
        <if test="query.departmentId != null">
            AND mp.department_id = #{query.departmentId}
        </if>
        <if test="query.businessMeetingId != null">
            AND mp.business_meeting_id = #{query.businessMeetingId}
        </if>
        <if test="query.startTimeBegin != null">
            AND mp.planned_start_time >= #{query.startTimeBegin}
        </if>
        <if test="query.startTimeEnd != null">
            AND mp.planned_start_time &lt;= #{query.startTimeEnd}
        </if>
        <if test="query.createUserId != null and query.createUserId != ''">
            AND mp.create_user_id = #{query.createUserId}
        </if>
        ORDER BY mp.planned_start_time DESC, mp.create_time DESC
    </select>

    <!-- 查询日历维度的会议规划 -->
    <select id="queryCalendar" resultType="cn.july.orch.meeting.domain.dto.MeetingPlanCalendarDTO">
        SELECT
            mp.id,
            mp.plan_name AS planName,
            mp.planned_start_time AS plannedStartTime,
            mp.planned_end_time AS plannedEndTime,
            mp.planned_duration AS plannedDuration,
            mp.status,
            ms.standard_name AS meetingStandardName,
            mp.priority_level AS priorityLevel,
            mp.department_name AS departmentName,
            mp.meeting_location AS meetingLocation,
            CASE
                WHEN mp.attendees IS NULL THEN 0
                ELSE JSON_LENGTH(mp.attendees)
            END AS attendeeCount,
            mp.create_user_name AS createUserName,
            CASE
                WHEN mp.priority_level = 4 THEN '#ff4d4f'  -- 紧急-红色
                WHEN mp.priority_level = 3 THEN '#fa8c16'  -- 高-橙色
                WHEN mp.priority_level = 2 THEN '#1890ff'  -- 中-蓝色
                ELSE '#52c41a'                             -- 低-绿色
            END AS backgroundColor
        FROM meeting_plan mp
        LEFT JOIN meeting_standard ms ON mp.meeting_standard_id = ms.id
        WHERE mp.planned_start_time >= #{query.startDate}
        AND mp.planned_start_time &lt; DATE_ADD(#{query.endDate}, INTERVAL 1 DAY)
        <if test="query.departmentId != null">
            AND mp.department_id = #{query.departmentId}
        </if>
        <if test="query.meetingStandardId != null">
            AND mp.meeting_standard_id = #{query.meetingStandardId}
        </if>
        <if test="query.createUserId != null and query.createUserId != ''">
            AND mp.create_user_id = #{query.createUserId}
        </if>
        ORDER BY mp.planned_start_time ASC
    </select>

</mapper>
