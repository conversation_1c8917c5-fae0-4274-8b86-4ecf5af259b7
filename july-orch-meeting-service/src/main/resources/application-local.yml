knife4j:
  enable: true
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://************:6379",
            "password": "July123456.",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
dromara:
  x-file-storage: #文件存储配置
    default-platform: minio-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: FHZArYPFxUOjadtHNGbo
        secret-key: iSjOzz2RB1ibZ2jV4WpOi2Tolp7kTZNfPDfB18rg
        end-point: http://coe-file-sit.pengfeijituan.com:9000/
        bucket-name: meeting-1
        domain: http://coe-file-sit.pengfeijituan.com:9000/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: ${spring.application.name}/
#    default-platform: huawei-obs-1 #默认使用的存储平台
#    thumbnail-suffix: ".min.jpg"
#    huawei-obs:
#      - platform: huawei-obs-1 # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: ERMZYOVKRBTVCAIBTIJ2
#        secret-key: phJPIFwjjdnFnjWCOG5mihaMhLBsMUnlneQED4mE
#        end-point: obs.cn-north-4.myhuaweicloud.com
#        bucket-name: test-kangjian
#        domain: https://test-kangjian.obs.cn-north-4.myhuaweicloud.com:443/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
#        base-path: ${spring.application.name}/
#    default-platform: amazon-s3-1
#    thumbnail-suffix: ".min.jpg"
#    amazon-s3: # 0.0.7 及以前的版本，配置名称是：aws-s3
#      - platform: amazon-s3-1 # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: AKLTNDA5ODZkYjcwYTNjNDllNzk2N2FkN2JjMjI5ZGNiZWQ
#        secret-key: T0dRM1l6SXpOalV6WkRBNE5HRmhZMkpqWmpVMU1HSXhZMlE1WkdaaE9UUQ==
#        end-point: tos-s3-cn-beijing.volces.com
#        bucket-name: pro-pf-meeting
#        domain: pro-pf-meeting.tos-cn-beijing.volces.com
#        base-path: ${spring.application.name}/
feishu:
  mini:
    appId: cli_a808965153f95013
    appSecret: xrOQJck37DRjTYvjyN5moR7glft5yaXD
july:
  database:
    multi:
      db:
        pf_orch_meiye:
          primary: true
          master:
            jdbcUrl: ***********************************************************************************************************************************************************************************
            username: root
            password: July123456.
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10